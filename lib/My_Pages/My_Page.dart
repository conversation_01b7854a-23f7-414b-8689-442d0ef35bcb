import 'package:flutter/material.dart';
import 'package:task7/My_Pages/Login.dart';
import 'package:task7/My_Widgets/Freelancer_widget.dart';
import 'package:task7/My_Widgets/Section_Widget.dart';

class MainApp extends StatelessWidget {
  MainApp({super.key});
  @override
  Widget build(BuildContext context) {
    final name = ModalRoute.of(context)!.settings.arguments as Login;
    return Scaffold(
      appBar: AppBar(
        leading: Text(name.email_controller.text),
        title: Image.asset('assets/logo.png'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Image.asset('assets/Frame.png'),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Image.asset('assets/basket.png'),
          ),
        ],
      ),
      body: Container(
        margin: EdgeInsets.only(top: 20),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Container(
                  height: 60,
                  width: 300,
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: const Color.fromARGB(255, 183, 183, 183),
                      width: 2,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.search, size: 40),
                      Text('Search here', style: TextStyle(fontSize: 20)),
                    ],
                  ),
                ),
                Image.asset('assets/secmenue.png'),
              ],
            ),
            Container(
              margin: EdgeInsets.only(top: 20, bottom: 20),
              height: 300,
              width: 450,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.white, Colors.blue.shade100],
                ),
              ),
              child: Row(
                children: [
                  Column(
                    children: [
                      Stack(
                        children: [
                          Text(
                            'Todays Deal',
                            style: TextStyle(
                              fontSize: 40,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 3
                                ..color = Colors.black,
                            ),
                          ),
                          Text(
                            'Todays Deal',
                            style: TextStyle(
                              fontSize: 40,
                              color: Colors.white, // لون التعبئة
                            ),
                          ),
                        ],
                      ),

                      Text(
                        '50% OFF',
                        style: TextStyle(
                          fontSize: 60,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      Text(''' 
 Et provident eos est dolore. Eum libero
 eligendi molestias aut et quibusdam
  aspernatur.
''', style: TextStyle(fontSize: 15)),
                      SizedBox(
                        height: 60,
                        width: 150,
                        child: Image.asset(
                          'assets/button.png',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 300,
                    width: 120,
                    child: Image.asset('assets/wom.png', fit: BoxFit.cover),
                  ),
                ],
              ),
            ),
            Section_Widget(section_text: 'Top Rated Freelancer'),
            SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Freelancer_widget(
                  image_path: 'assets/wom.png',
                  name: 'John Doe',
                  jop: 'Flutter',
                ),
                Freelancer_widget(
                  image_path: 'assets/wom.png',
                  name: 'John Doe',
                  jop: 'Web ',
                ),
                Freelancer_widget(
                  image_path: 'assets/wom.png',
                  name: 'John Doe',
                  jop: 'Tester',
                ),
              ],
            ),
            Section_Widget(section_text: 'Top Servsis'),
          ],
        ),
      ),
    );
  }
}
