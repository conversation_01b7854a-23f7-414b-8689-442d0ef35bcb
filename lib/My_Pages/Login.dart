import 'package:flutter/material.dart';
import 'package:task7/My_Pages/My_Page.dart';
import 'package:task7/My_Pages/Signup.dart';
import 'package:task7/My_Widgets/textfiled.dart';
import 'package:task7/routs.dart';

class Login extends StatelessWidget {
  Login({super.key});
  GlobalKey<FormState> form_key = GlobalKey<FormState>();
  var email_controller = TextEditingController();
  var password_controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Log In'), centerTitle: true),
      body: Center(
        child: Form(
          key: form_key,
          child: Column(
            children: [
              Image.network(
                'https://cdn-icons-png.flaticon.com/512/14018/14018816.png',
                height: 200,
              ),
              My_Textfield(
                hint_text: 'Email',
                is_password: false,
                controller: email_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter email';
                  if (!value.contains('@')) return 'Please enter valid email';
                  if (!value.contains('.')) return 'Please enter valid email';
                  return null;
                },
              ),
              My_Textfield(
                hint_text: 'Password',
                is_password: true,
                controller: password_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter password';
                  if (value.length < 6)
                    return 'Password must be at least 6 characters';
                  return null;
                },
              ),
              SizedBox(height: 20),
              SizedBox(
                child: TextButton(
                  onPressed: () =>
                      Navigator.pushReplacementNamed(
                        context, My_Routs.signup ,
                        
                        ),
                  child: Text(
                    'Sign In',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 17,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),

              SizedBox(
                width: 150,
                height: 60,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 0, 0, 0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () => _login(context),
                  child: Text(
                    'Log In',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _login(BuildContext context) {
   if(form_key.currentState!.validate()){
   Navigator.pushReplacementNamed(context, 
  My_Routs.main , 
  arguments: this,
  );
   }
  }
}
