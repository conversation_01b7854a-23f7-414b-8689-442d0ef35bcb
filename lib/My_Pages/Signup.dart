import 'package:flutter/material.dart';
import 'package:task7/My_Pages/Login.dart';
import 'package:task7/My_Pages/My_Page.dart';
import 'package:task7/My_Widgets/textfiled.dart';
import 'package:task7/routs.dart';

class Signup extends StatelessWidget {
  Signup({super.key});
  var email_controller = TextEditingController();
  var password_controller = TextEditingController();
  var confirmpassowrd_controller = TextEditingController();
  var phone_controller = TextEditingController();
  final GlobalKey<FormState> form_key = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Sign Up'), centerTitle: true),
      body: Center(
        child: Form(
          key: form_key,
          child: Column(
            children: [
              Image.network(
                'https://cdn-icons-png.freepik.com/256/5822/5822138.png?semt=ais_white_label',
                height: 200,
              ),
              My_Textfield(
                hint_text: 'Email',
                is_password: false,
                controller: email_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter email';
                  if (!value.contains('@')) return 'Please enter valid email';
                  if (!value.contains('.')) return 'Please enter valid email';
                  return null;
                },
              ),
              My_Textfield(
                hint_text: 'Password',
                is_password: true,
                controller: password_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter password';
                  if (value.length < 6)
                    return 'Password must be at least 6 characters';
                  return null;
                },
              ),
              My_Textfield(
                is_password: true,
                hint_text: 'Confirm Passowrd',
                controller: confirmpassowrd_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter confirm password';
                  if (value != password_controller.text) {
                    return 'Password does not match';
                  }
                  return null;
                },
              ),
              My_Textfield(
                hint_text: 'Phone Number',
                is_password: true,
                controller: phone_controller,
                validator: (value) {
                  if (value!.isEmpty) return 'Please enter phone number';
                  if (value.length < 10) {
                    return 'Please enter valid phone number';
                  }
                  return null;
                },
              ),
              SizedBox(height: 20),

              SizedBox(
                child: TextButton(
                  onPressed: () =>
                      Navigator.pushReplacementNamed(context, My_Routs.login),
                  child: Text(
                    'Log In',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 17,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),

              SizedBox(
                width: 150,
                height: 60,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 0, 0, 0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () {
                    _login(context);
                  },
                  child: Text(
                    'Sign Up',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _login(BuildContext context) {
    if (form_key.currentState!.validate()) {
      Navigator.pushReplacementNamed(
        context,
        My_Routs.main,
      );
    }
  }
}
