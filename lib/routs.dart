import 'package:go_router/go_router.dart';
import 'package:task7/My_Pages/Login.dart';
import 'package:task7/My_Pages/My_Page.dart';
import 'package:task7/My_Pages/Signup.dart';

class My_Routs {
  static const String login = '/login';
  static const String signup = '/signup';
  static const String main = '/mainapp';
}

final  My_routes = GoRoute(
  path: '/',
  routes: [
    GoRoute(
      path: My_Routs.login, builder: 
      (context, state) => Login()),
    GoRoute(path: My_Routs.signup, builder: 
    (context, state) => Signup()),
    GoRoute(path: My_Routs.main, builder: 
    (context, state) => MainApp()),
  ]
);
