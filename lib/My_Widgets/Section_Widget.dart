import 'package:flutter/material.dart';

class Section_Widget extends StatelessWidget {
  const Section_Widget({super.key , required this.section_text});
  final String section_text;
  @override
  Widget build(BuildContext context) {
    return Container(
                height: 100,
                padding: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade100, Colors.white],
                  ),
                ), 
    child:Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          section_text,
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        Text(
          'View All',
          style: TextStyle(
            fontSize: 19,
            fontWeight: FontWeight.bold,
            color: const Color.fromARGB(255, 3, 86, 154),
            decoration: TextDecoration.underline,
            decorationThickness: 1,
          ),
        ),
      ],
    ));
  }
}
