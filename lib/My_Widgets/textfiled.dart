import 'package:flutter/material.dart';

class My_Textfield extends StatelessWidget {
  My_Textfield({
    super.key,
    required this.hint_text,
    required this.is_password,
    required this.controller,
    this.validator,
  });
  final String hint_text;
  final bool is_password;
  final TextEditingController controller;
  String? Function(String?)? validator;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      child: TextFormField(
        validator: validator,
        controller: controller,
        obscureText: is_password,
        decoration: InputDecoration(
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
          hintText: hint_text,
        ),
      ),
    );
  }
}
