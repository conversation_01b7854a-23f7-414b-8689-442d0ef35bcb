import 'package:flutter/material.dart';

class Freelancer_widget extends StatelessWidget {
  const Freelancer_widget({
    super.key,
    required this.image_path,
    required this.name, required this.jop,
  });
  final String image_path;
  final String name;
  final String jop;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CircleAvatar(radius: 40, backgroundImage: AssetImage(image_path)),
        Text(name),
        Text(
          jop,
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
      ],
    );
  }
}
